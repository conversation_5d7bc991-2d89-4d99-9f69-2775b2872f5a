# RebelDotAIChallenge - Docker Setup

This document explains how to run the RebelDotAIChallenge application using Docker and Docker Compose.

## Prerequisites

- Docker and Docker Compose installed
- OpenAI API key

## Quick Start

1. **Clone the repository and navigate to the project directory**

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

3. **Build and start all services**
   ```bash
   docker-compose up --build
   ```

4. **Access the application**
   - API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - PostgreSQL: localhost:6024
   - Redis: localhost:6379

## Services

### Core Services
- **api**: FastAPI application (port 8000)
- **postgres**: PostgreSQL with pgvector extension (port 6024)
- **redis**: Redis for Celery message broker (port 6379)
- **celery-worker**: Background task processor
- **data-init**: One-time data initialization service

### Data Initialization

The `data-init` service automatically loads default FAQ data from `.data/rag_data/data.json` into the vector database when the application starts. This ensures the default collection is populated with initial data.

## Environment Variables

Required environment variables (set in `.env`):
- `OPENAI_API_KEY`: Your OpenAI API key
- `API_KEY`: Security key for API access (optional)

## Development

### Running Individual Services

```bash
# Start only database and Redis
docker-compose up postgres redis

# Start API only (after database is ready)
docker-compose up api

# Start Celery worker only
docker-compose up celery-worker
```

### Viewing Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f api
docker-compose logs -f celery-worker
```

### Rebuilding After Code Changes

```bash
# Rebuild and restart
docker-compose up --build

# Or rebuild specific service
docker-compose build api
docker-compose up api
```

## Data Persistence

- PostgreSQL data: Stored in `postgres_data` Docker volume
- Redis data: Stored in `redis_data` Docker volume
- Cache files: Mounted from local `.data/cache` directory
- RAG data: Mounted from local `.data/rag_data` directory

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is ready
docker-compose exec postgres pg_isready -U langchain -d langchain
```

### Celery Worker Issues
```bash
# Check Celery worker status
docker-compose exec celery-worker celery -A rebeldotaichallenge.tasks.celery_app inspect active
```

### API Health Check
```bash
curl http://localhost:8000/
```

## Stopping Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This deletes all data)
docker-compose down -v
```
