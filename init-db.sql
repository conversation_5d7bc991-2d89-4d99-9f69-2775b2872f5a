-- Initialize database with pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE langchain TO langchain;

-- Create schema if needed
CREATE SCHEMA IF NOT EXISTS public;
GRANT ALL ON SCHEMA public TO langchain;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO langchain;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO langchain;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO langchain;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO langchain;
